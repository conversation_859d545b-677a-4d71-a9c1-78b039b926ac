package service;

import com.fasterxml.jackson.databind.ObjectMapper;
import commons.GeminiCommons;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import utils.Defaults;
import utils.ReflectionUtils;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Service for generating and managing field translations using Gemini AI
 * Orchestrates field extraction, AI translation generation, and storage/retrieval
 * 
 * <AUTHOR>
 */
public class FieldTranslationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(FieldTranslationService.class.getName());
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    // In-memory cache for translations
    private static final Map<String, String> translationCache = new ConcurrentHashMap<>();
    
    // File path for storing translations
    private static final String TRANSLATIONS_FILE_PATH = "translations/field_translations.json";
    
    private static boolean initialized = false;

    /**
     * Initialize the translation service
     * This method should be called during application startup
     */
    public static void initializeTranslations() {
        if (!Defaults.ENABLE_FIELD_TRANSLATIONS) {
            LOGGER.info("Field translations are disabled in configuration");
            return;
        }

        if (initialized) {
            LOGGER.warn("Field translation service is already initialized");
            return;
        }

        LOGGER.info("Initializing field translation service...");
        long startTime = System.currentTimeMillis();

        try {
            // First, try to load existing translations from file
            loadTranslationsFromFile();

            // Extract field names from POJOs
            Set<String> fieldNames = ReflectionUtils.extractFieldNamesFromChangeStreamCollections();
            
            if (fieldNames.isEmpty()) {
                LOGGER.warn("No field names extracted from CHANGE_STREAMS_COLLECTIONS");
                initialized = true;
                return;
            }

            // Check if we need to generate new translations
            Set<String> missingTranslations = findMissingTranslations(fieldNames);
            
            if (!missingTranslations.isEmpty()) {
                LOGGER.info("Found {} fields without translations, generating using Gemini AI", missingTranslations.size());
                generateAndStoreTranslations(missingTranslations);
            } else {
                LOGGER.info("All field translations are already available");
            }

            initialized = true;
            long endTime = System.currentTimeMillis();
            LOGGER.info("Field translation service initialized in {} ms. Total translations: {}", 
                       (endTime - startTime), translationCache.size());

        } catch (Exception e) {
            LOGGER.error("Failed to initialize field translation service", e);
            // Don't throw exception to avoid breaking application startup
        }
    }

    /**
     * Get translation for a specific field name
     * 
     * @param fieldName The field name to translate
     * @return The Italian translation or the original field name if no translation exists
     */
    public static String getTranslation(String fieldName) {
        if (!initialized) {
            LOGGER.warn("Translation service not initialized, returning original field name: {}", fieldName);
            return fieldName;
        }

        return translationCache.getOrDefault(fieldName, fieldName);
    }

    /**
     * Get all available translations
     * 
     * @return Map of field name -> translation
     */
    public static Map<String, String> getAllTranslations() {
        return new HashMap<>(translationCache);
    }

    /**
     * Force regeneration of all translations
     * This method can be called manually if needed
     */
    public static void regenerateAllTranslations() {
        LOGGER.info("Force regenerating all field translations...");
        
        try {
            // Clear existing cache
            translationCache.clear();
            
            // Extract all field names
            Set<String> fieldNames = ReflectionUtils.extractFieldNamesFromChangeStreamCollections();
            
            if (!fieldNames.isEmpty()) {
                generateAndStoreTranslations(fieldNames);
                LOGGER.info("Successfully regenerated {} translations", translationCache.size());
            }
            
        } catch (Exception e) {
            LOGGER.error("Failed to regenerate translations", e);
        }
    }

    /**
     * Load existing translations from file
     */
    private static void loadTranslationsFromFile() {
        try {
            File translationsFile = new File(TRANSLATIONS_FILE_PATH);
            
            if (translationsFile.exists()) {
                LOGGER.debug("Loading existing translations from file: {}", TRANSLATIONS_FILE_PATH);
                
                @SuppressWarnings("unchecked")
                Map<String, String> loadedTranslations = objectMapper.readValue(translationsFile, Map.class);
                
                translationCache.putAll(loadedTranslations);
                LOGGER.info("Loaded {} existing translations from file", loadedTranslations.size());
            } else {
                LOGGER.debug("No existing translations file found at: {}", TRANSLATIONS_FILE_PATH);
            }
            
        } catch (IOException e) {
            LOGGER.warn("Failed to load existing translations from file", e);
        }
    }

    /**
     * Find field names that don't have translations yet
     */
    private static Set<String> findMissingTranslations(Set<String> fieldNames) {
        Set<String> missing = new LinkedHashSet<>();
        
        for (String fieldName : fieldNames) {
            if (!translationCache.containsKey(fieldName)) {
                missing.add(fieldName);
            }
        }
        
        return missing;
    }

    /**
     * Generate translations using Gemini AI and store them
     */
    private static void generateAndStoreTranslations(Set<String> fieldNames) throws IOException {
        if (fieldNames.isEmpty()) {
            return;
        }

        // Convert to list for API call
        List<String> fieldNamesList = new ArrayList<>(fieldNames);
        
        // Generate translations using Gemini AI
        Map<String, String> newTranslations = GeminiCommons.generateFieldTranslations(fieldNamesList);
        
        if (newTranslations.isEmpty()) {
            LOGGER.warn("No translations generated by Gemini AI");
            return;
        }

        // Add to cache
        translationCache.putAll(newTranslations);
        
        // Save to file
        saveTranslationsToFile();
        
        LOGGER.info("Generated and stored {} new translations", newTranslations.size());
    }

    /**
     * Save current translations to file
     */
    private static void saveTranslationsToFile() {
        try {
            File translationsFile = new File(TRANSLATIONS_FILE_PATH);
            
            // Create directory if it doesn't exist
            File parentDir = translationsFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                boolean created = parentDir.mkdirs();
                if (!created) {
                    LOGGER.warn("Failed to create translations directory: {}", parentDir.getAbsolutePath());
                }
            }
            
            // Write translations to file
            objectMapper.writerWithDefaultPrettyPrinter().writeValue(translationsFile, translationCache);
            LOGGER.debug("Saved {} translations to file: {}", translationCache.size(), TRANSLATIONS_FILE_PATH);
            
        } catch (IOException e) {
            LOGGER.error("Failed to save translations to file", e);
        }
    }

    /**
     * Get translation statistics
     */
    public static Map<String, Object> getTranslationStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("initialized", initialized);
        stats.put("totalTranslations", translationCache.size());
        stats.put("translationsEnabled", Defaults.ENABLE_FIELD_TRANSLATIONS);
        
        if (initialized) {
            Set<String> allFieldNames = ReflectionUtils.extractFieldNamesFromChangeStreamCollections();
            stats.put("totalFields", allFieldNames.size());
            stats.put("missingTranslations", findMissingTranslations(allFieldNames).size());
        }
        
        return stats;
    }

    /**
     * Check if the service is initialized
     */
    public static boolean isInitialized() {
        return initialized;
    }
}
