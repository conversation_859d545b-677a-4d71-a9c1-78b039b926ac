package utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import service.FieldTranslationService;

import java.util.Map;
import java.util.Set;

/**
 * Utility class for testing the field translation functionality
 * This can be used to manually test the translation system
 * 
 * <AUTHOR>
 */
public class TranslationTestUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(TranslationTestUtils.class.getName());

    /**
     * Test the field extraction functionality
     */
    public static void testFieldExtraction() {
        LOGGER.info("=== Testing Field Extraction ===");
        
        try {
            Set<String> fieldNames = ReflectionUtils.extractFieldNamesFromChangeStreamCollections();
            
            LOGGER.info("Extracted {} field names from CHANGE_STREAMS_COLLECTIONS:", fieldNames.size());
            for (String fieldName : fieldNames) {
                LOGGER.info("  - {}", fieldName);
            }
            
        } catch (Exception e) {
            LOGGER.error("Error during field extraction test", e);
        }
    }

    /**
     * Test the translation service functionality
     */
    public static void testTranslationService() {
        LOGGER.info("=== Testing Translation Service ===");
        
        try {
            // Initialize if not already done
            if (!FieldTranslationService.isInitialized()) {
                FieldTranslationService.initializeTranslations();
            }
            
            // Get statistics
            Map<String, Object> stats = FieldTranslationService.getTranslationStats();
            LOGGER.info("Translation service statistics:");
            for (Map.Entry<String, Object> entry : stats.entrySet()) {
                LOGGER.info("  {} = {}", entry.getKey(), entry.getValue());
            }
            
            // Test some translations
            String[] testFields = {"claimNumber", "provinceCode", "universalClass", "email", "name", "description"};
            
            LOGGER.info("Testing translations for sample fields:");
            for (String field : testFields) {
                String translation = FieldTranslationService.getTranslation(field);
                LOGGER.info("  {} -> {}", field, translation);
            }
            
        } catch (Exception e) {
            LOGGER.error("Error during translation service test", e);
        }
    }

    /**
     * Test detailed field information extraction
     */
    public static void testDetailedFieldInfo() {
        LOGGER.info("=== Testing Detailed Field Information ===");
        
        try {
            Map<String, java.util.List<ReflectionUtils.FieldInfo>> detailedInfo = 
                ReflectionUtils.extractDetailedFieldInfo();
            
            LOGGER.info("Detailed field information for {} collections:", detailedInfo.size());
            
            for (Map.Entry<String, java.util.List<ReflectionUtils.FieldInfo>> entry : detailedInfo.entrySet()) {
                String collectionName = entry.getKey();
                java.util.List<ReflectionUtils.FieldInfo> fieldInfoList = entry.getValue();
                
                LOGGER.info("Collection '{}' has {} fields:", collectionName, fieldInfoList.size());
                for (ReflectionUtils.FieldInfo fieldInfo : fieldInfoList) {
                    LOGGER.info("  - {}", fieldInfo);
                }
            }
            
        } catch (Exception e) {
            LOGGER.error("Error during detailed field info test", e);
        }
    }

    /**
     * Run all tests
     */
    public static void runAllTests() {
        LOGGER.info("=== Running All Translation Tests ===");
        
        testFieldExtraction();
        testDetailedFieldInfo();
        testTranslationService();
        
        LOGGER.info("=== All Tests Completed ===");
    }

    /**
     * Test method that can be called from a controller or main method
     */
    public static String getTestResults() {
        StringBuilder results = new StringBuilder();
        
        try {
            // Test field extraction
            Set<String> fieldNames = ReflectionUtils.extractFieldNamesFromChangeStreamCollections();
            results.append("Field Extraction: ").append(fieldNames.size()).append(" fields found\n");
            
            // Test translation service
            if (!FieldTranslationService.isInitialized()) {
                FieldTranslationService.initializeTranslations();
            }
            
            Map<String, Object> stats = FieldTranslationService.getTranslationStats();
            results.append("Translation Service Stats:\n");
            for (Map.Entry<String, Object> entry : stats.entrySet()) {
                results.append("  ").append(entry.getKey()).append(" = ").append(entry.getValue()).append("\n");
            }
            
            // Test sample translations
            results.append("Sample Translations:\n");
            String[] testFields = {"claimNumber", "provinceCode", "email", "name"};
            for (String field : testFields) {
                String translation = FieldTranslationService.getTranslation(field);
                results.append("  ").append(field).append(" -> ").append(translation).append("\n");
            }
            
        } catch (Exception e) {
            results.append("Error during testing: ").append(e.getMessage()).append("\n");
            LOGGER.error("Error during testing", e);
        }
        
        return results.toString();
    }
}
