package utils;

import core.Routes;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Defaults {

    public static String PROJECT_NAME = "tigani";
    public static final String BACKUP_DB_SUFFIX = "_backup";
    public static String BACKUP_DATABASE_NAME = PROJECT_NAME + BACKUP_DB_SUFFIX;
    public static String GOOGLE_RECAPTCHA_SECRET = "inserirechiave";
    public static String GOOGLE_RECAPTCHA_SITE = "inserirechiave";

    public static String FIRST_PAGE = Routes.BE_USER_COLLECTION;
    public static String MANIFEST_NAME = "META-INF/MANIFEST.MF";

    // sezione lingue
    public static String LANGUAGE = "it";
    public static List<String> ALL_LANGUAGES = new ArrayList<>(Arrays.asList("it", "en", "de", "fr", "es"));
    public static List<String> AVAILABLE_LANGUAGES = new ArrayList<>(); // loaded on Core.java, #start()
    public static List<String> VISIBLE_LANGUAGES = new ArrayList<>(); // loaded on Core.java, #start()
    public static String DEFAULT_USER_LANGUAGE = "it";

    // collection names for initialization
    public static List<String> COLLECTION_NAMES = new ArrayList<>(Arrays.asList("path"));

    // extra modules
    public static Boolean ENABLE_QUERY_LOGS = true;             // salva a db chi fa inserimenti, update, delete

    // MongoDB Change Streams for backup
    public static Boolean ENABLE_CHANGE_STREAMS_BACKUP = true;  // enable/disable change streams backup functionality
    public static List<String> CHANGE_STREAMS_COLLECTIONS = new ArrayList<>(Arrays.asList("Brand", "Channel", "Company", "InsuranceCompany", "InsuranceProvenanceType", "Settings", "User", "Warranty", "WarrantyDetails", "WarrantyType", "User")); // collections to monitor for changes
    public static String REDIS_HOSTNAME = "localhost";
    public static String USER_SESSION_COOKIE = Defaults.PROJECT_NAME + "_user_session";
    public static String REDIS_PREFIX = Defaults.PROJECT_NAME + "_resource";
    public static int SESSION_DURATION = 28800;
    public static final long STATIC_RESOURCE_EXPIRATION_TIME = 2592000L;

    // italiana API KEY
    public static String ITALIANA_PRODUCTION_GATEWAY = "https://apigw-prod.grupporealemutua.it/gateway";
    public static String ITALIANA_PRODUCTION_API_KEY = "467badce-8c2d-4b75-91da-fc6c3c1ddf6e";

    public static String ITALIANA_BRANDS_API = "auto/veicoli/marche/v1.0";
    public static String ITALIANA_MODELS_API = "auto/veicoli/modelli/v1.0";
    public static String ITALIANA_MODELS_SETUP_API = "auto/veicoli/allestimenti/v1.0";
    public static String ITALIANA_CHANNEL = "HARLEYDAVIDSON";
    public static String ITALIANA_COMPANY_CODE = "ITA";
    public static String ITALIANA_AGENCY_CODE = "AZB";
    public static String ITALIANA_AGENT_CODE = "A3AZB01";
    public static String ITALIANA_VEHICLE_CLASS = "MOTOCICLO";
}
