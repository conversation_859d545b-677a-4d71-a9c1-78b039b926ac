{"id": "Identificativo", "language": "<PERSON><PERSON>", "availableLanguages": "Lingue Disponibili", "parentId": "ID Genitore", "creation": "Data Creazione", "lastUpdate": "Ultimo <PERSON>", "archived": "Archiviato", "cancelled": "<PERSON><PERSON><PERSON>", "code": "Codice", "description": "Descrizione", "title": "<PERSON><PERSON>", "name": "Nome", "email": "Email", "phoneNumber": "Numero di Telefono", "fullname": "Nome <PERSON>to", "tin": "<PERSON><PERSON>", "vatNumber": "Partita IVA", "externalCode": "<PERSON><PERSON>", "endpoint": "Endpoint", "apiKey": "Chiave API", "claimNumber": "Numero Sinistro", "provinceCode": "Codice Provincia", "universalClass": "Classe Universale", "premiumValue": "<PERSON>ore <PERSON>", "warrantyId": "Garanzia", "insuranceProvenanceTypeId": "Tipo Provenienza Assicurativa", "warrantyTypeId": "<PERSON><PERSON><PERSON>", "insuranceCompanyId": "Compagnia Assicurativa", "criteriaFields": "<PERSON><PERSON> Criteri", "brandModelIds": "<PERSON><PERSON>", "warrantyIds": "<PERSON><PERSON><PERSON>", "insuranceProvenanceTypeIds": "Tipi Provenienza Assicurativa", "capacityLimit": "Limite Capacità", "companyId": "Azienda", "fileIds": "File", "logoImageId": "Logo", "defaultLanguage": "Lingua Predefinita", "visibleLanguages": "Lingue Visibili", "lastname": "Cognome", "languages": "Lingue", "birthDate": "Data di Nascita", "informations": "Informazioni", "registrationSendDate": "Data Invio Registrazione", "registrationDate": "Data Registrazione", "registrationToken": "Token Registrazione", "username": "Nome Utente", "password": "Password", "profileType": "T<PERSON>o Profilo", "imageId": "<PERSON><PERSON><PERSON><PERSON>", "icon": "Icona", "codice": "Codice", "descrizione": "Descrizione", "brandId": "<PERSON><PERSON>", "modelId": "<PERSON><PERSON>", "annoImmatricolazione": "Anno Immatricolazione", "meseImmatricolazione": "Mese Immatricolazione", "valoreAssicurato": "<PERSON><PERSON>"}